namespace Final_E_Receipt.Notifications.DTOs
{
    public class CreateEmailConfigurationDTO
    {
        public string OrganizationId { get; set; }
        public string SmtpServer { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; }
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
    }

    public class UpdateEmailConfigurationDTO
    {
        public string SmtpServer { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; }
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
    }
}